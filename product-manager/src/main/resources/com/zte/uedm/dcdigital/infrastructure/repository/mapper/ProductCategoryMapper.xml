<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductCategoryMapper">
    <resultMap id="ProductCategoryResult" type="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryPo">
        <!-- 主键字段 -->
        <id property="id" column="id"/>

        <!-- 基础字段 -->
        <result property="productName" column="product_name"/>
        <result property="description" column="description"/>
        <result property="productLineNo" column="product_line_no"/>
        <result property="productLineName" column="product_line_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="pathName" column="path_name"/>
        <result property="pathId" column="path_id"/>
        <result property="nodeType" column="node_type"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>

        <!-- 联表查询字段（不存在于product_category表中） -->
        <result property="productNo" column="product_no"/>
        <result property="productLevel" column="product_level"/>
    </resultMap>
    <!-- Started by AICoder, pid:4795fe8365e6da814eed0a3ea04d6831e9d0a126 -->
    <select id="selectByCondition"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryPo">
        SELECT
        pc.id,
        pc.product_name,
        pc.description,
        pc.product_line_no,
        pc.product_line_name,
        pc.parent_id,
        pc.path_name,
        pc.path_id,
        pc.node_type,
        pc.sort_order,
        pc.create_time,
        pc.update_time,
        pc.create_by,
        pc.update_by,
        ex.product_no,
        ex.product_level
        FROM product_category pc
        LEFT JOIN product_category_extra_info ex ON ex.product_category_id = pc.id
        WHERE pc.node_type = #{nodeType}
        <if test="categoryName != null and categoryName != ''">
            AND pc.product_name LIKE CONCAT('%', #{categoryName}, '%')
        </if>
        <if test="parentId != null and parentId != ''">
            AND pc.parent_id = #{parentId}
        </if>
        ORDER BY  ex.product_no,pc.product_name
    </select>
    <!-- Ended by AICoder, pid:4795fe8365e6da814eed0a3ea04d6831e9d0a126 -->

    <select id="queryAllProductCategoryList" resultMap="ProductCategoryResult">
        SELECT
            pc.id,
            pc.product_name,
            pc.description,
            pc.product_line_no,
            pc.product_line_name,
            pc.parent_id,
            pc.path_name,
            pc.path_id,
            pc.node_type,
            pc.sort_order,
            ex.product_no,
            ex.product_level,
            pc.create_time,
            pc.update_time,
            pc.create_by,
            pc.update_by,
            pc.sort_order
        FROM product_category pc
                 LEFT JOIN product_category_extra_info ex ON ex.product_category_id = pc.id
        ORDER BY  pc.path_name, ex.product_no
    </select>

    <select id="queryProductSubclasses"
            resultType="com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo">
        SELECT
        a.id AS id,
        a.product_name AS productName,
        a.description AS description,
        a.parent_id as parentId,
        a.path_name as pathName,
        a.path_id as pathId,
        a.node_type as nodeType,
        a.create_time as createTime,
        a.update_time as updateTime,
        a.create_by as createBy,
        a.update_by as updateBy,
        b.product_level as productLevel,
        b.product_component as productComponent,
        b.non_standard_items as nonStandardItems,
        b.material_type as materialType
        FROM product_category a
        left join product_category_extra_info b on b.product_category_id = a.id
        where node_type = #{nodeType} and a.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="queryUserProductSubcategory"
            resultType="com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductSubcategoryVo">
        SELECT
        pc.id as id,
        pc.product_name as productName,
        pc.description as description,
        pl.product_line_no as productLineNo,
        pl.product_line_name as productLineName,
        pc.parent_id as parentId,
        pc.path_name as pathName,
        ex.product_level as productLevel,
        ex.product_component as productComponent,
        ex.non_standard_items as nonStandardItems,
        ex.material_type as materialType,
        pc.path_id as pathId,
        pc.node_type as nodeType
        FROM public.product_category pc
        LEFT JOIN public.product_category pl ON
        pl.node_type = 1 AND -- 确保我们只关联到产品线类型的记录
        split_part(pc.path_id, '/', 1) = pl.id::text -- 取path_id中第一个ID与pl.id比较
        left join product_category_extra_info ex on ex.product_category_id = pc.id
        where pc.node_type = #{nodeType} and pc.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY pc.product_name
    </select>
    <select id="selectCurentNodeAllChildNode"
            resultType="com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity">
        WITH RECURSIVE child_tree AS (
        SELECT id, product_name, description, product_line_no, product_line_name, parent_id, path_name, path_id, node_type, create_time, update_time, create_by, update_by
        FROM product_category
        WHERE id = #{currentNodeId}
        UNION ALL
        SELECT t.id, t.product_name, t.description, t.product_line_no, t.product_line_name, t.parent_id, t.path_name, t.path_id, t.node_type, t.create_time, t.update_time, t.create_by, t.update_by
        FROM product_category t
        INNER JOIN child_tree c ON t.parent_id = c.id
        )
        SELECT id, product_name, description, product_line_no, product_line_name, parent_id, path_name, path_id, node_type, create_time, update_time, create_by, update_by FROM child_tree;

    </select>
    <select id="queryByIdAndName" resultType="java.lang.Integer">
        SELECT COUNT(*) AS duplicate_exists
        FROM product_category pc
        WHERE pc.product_name = #{name}
        AND pc.node_type = (SELECT node_type FROM product_category WHERE id = #{id}) -- 获取当前编辑记录的类型
        AND pc.parent_id = (SELECT parent_id FROM product_category WHERE id = #{id}) -- 同一父级
        AND pc.id != #{id} -- 排除当前编辑记录
    </select>
    <select id="queryAllProductSubcategory"
            resultType="com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductSubcategoryVo">
        SELECT
        pc.id as id,
        pc.product_name as productName,
        pc.description as description,
        pl.product_line_no as productLineNo,
        pl.product_line_name as productLineName,
        pc.parent_id as parentId,
        pc.path_name as pathName,
        ex.product_level as productLevel,
        ex.product_component as productComponent,
        ex.non_standard_items as nonStandardItems,
        ex.product_no as productNo,
        ex.material_type as materialType,
        pc.path_id as pathId,
        pc.node_type as nodeType,
        pc.sort_order as sortOrder
        FROM public.product_category pc
        LEFT JOIN public.product_category pl ON
        pl.node_type = 1 AND split_part(pc.path_id, '/', 1) = pl.id::text
        left join product_category_extra_info ex on ex.product_category_id = pc.id
        where pc.node_type = #{nodeType} ORDER BY pc.path_name, ex.product_no
    </select>
    <!-- Started by AICoder, pid:n33c86d752g7ddf1400b080690f4b1033ef68c8e -->
    <select id="selectByCategoryId" resultType="string">
        <!-- 根据产品类别ID查询子类别ID -->
        SELECT id
        FROM product_category
        WHERE parent_id = #{productCategoryId}
    </select>
    <!-- Ended by AICoder, pid:n33c86d752g7ddf1400b080690f4b1033ef68c8e -->

    <select id="selectByName"
            resultType="com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductSubcategoryVo">
        SELECT
        id as id,
        product_name as productName
        FROM public.product_category
        where node_type = #{nodeType} and product_name = #{categoryName}
    </select>

    <!-- Started by AICoder, pid:kbdc5r98da9da9f1441f08ef40376c1aa1b3b653 -->
     <!-- 根据节点类型查询产品子类别信息 -->
    <select id="selectByNodeType"
                resultType="com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductSubcategoryVo">
            <!-- 查询产品子类别信息，返回包含ID和产品名称的 ProductSubcategoryVo 对象列表 -->
            SELECT
            id as id,
            product_name as productName
            FROM public.product_category
            where node_type = #{nodeType}
    </select>
    <!-- Ended by AICoder, pid:kbdc5r98da9da9f1441f08ef40376c1aa1b3b653 -->
    <select id="selectIdsByParentId" resultType="com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo">
        WITH RECURSIVE category_tree AS (
        SELECT id, product_name, path_name, node_type FROM product_category WHERE parent_id = #{parentId}
        UNION ALL
        SELECT pc.id, pc.product_name, pc.path_name, pc.node_type
        FROM product_category pc
        JOIN category_tree ct ON pc.parent_id = ct.id
        )
        SELECT id, product_name, path_name FROM category_tree where node_type = 3;
    </select>

    <select id="getProductSubcategoryId" resultType="string">
        SELECT id FROM product_category where node_type = 3;
    </select>

    <select id="getAllBigProductCategoryByLineNo"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryPo">
        SELECT child.*
        FROM product_category child
        JOIN product_category parent ON child.parent_id = parent.id
        WHERE parent.product_line_no = #{productLineNo};
    </select>

    <select id="getAllSmallProductNoByBigId"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryPo">
        SELECT
        pc.id id,
        pc.product_name,
        pcei.product_no
        FROM product_category pc
        JOIN product_category_extra_info pcei ON pc.id = pcei.product_category_id
        WHERE pc.parent_id IN
        <foreach collection="bigCategoryIds" item="bigId" open="(" separator="," close=")">
            #{bigId}
        </foreach>
        ORDER BY pc.product_name, pcei.product_no;
    </select>

    <!-- 根据父级ID查询所有子孙产品小类及其所属大类信息 -->
    <select id="querySubcategoriesWithCategoryByParentId"
            resultType="com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductSubcategoryWithCategoryVo">
        WITH RECURSIVE category_tree AS (
        -- 查找传入ID的节点类型
        SELECT id, product_name, parent_id, node_type, 1 as level
        FROM product_category
        WHERE 1=1
        <if test="parentId != null and parentId != ''">
           and id = #{parentId}
        </if>
        UNION ALL

        -- 递归查找所有子节点
        SELECT pc.id, pc.product_name, pc.parent_id, pc.node_type, ct.level + 1
        FROM product_category pc
        INNER JOIN category_tree ct ON pc.parent_id = ct.id
        )
        SELECT DISTINCT
        sub.id as subcategoryId,
        sub.product_name as subcategoryName,
        cat.id as categoryId,
        cat.product_name as categoryName
        FROM category_tree sub
        JOIN product_category cat ON sub.parent_id = cat.id
        WHERE sub.node_type = 3
        AND cat.node_type = 2
        ORDER BY cat.product_name, sub.product_name
    </select>

    <!-- 根据产品小类ID列表和时间节点范围，统计每个产品小类在指定时间范围内的物料数据指标 -->
    <select id="queryMaterialStatisticsByTimeRange"
            resultType="com.zte.uedm.dcdigital.interfaces.web.product.vo.MaterialStatisticsVo">
        SELECT
            cat.product_category_id AS productCategoryId,
            tp.time_point AS timePoint,
            COALESCE(COUNT(m.id), 0) AS totalMaterialCount,
            COALESCE(COUNT(CASE WHEN m.material_status IN ('3', '8') THEN 1 END), 0) AS listedMaterialCount,
            0 AS listedMaterialChange,
            COALESCE(COUNT(CASE WHEN m.material_status NOT IN ('3', '8') THEN 1 END), 0) AS unlistedMaterialCount,
            0 AS unlistedMaterialChange
        FROM (
            <foreach collection="timePoints" item="timePoint" separator=" UNION ALL ">
                SELECT #{timePoint} AS time_point
            </foreach>
        ) tp
        CROSS JOIN (
            <foreach collection="queryDto.productCategoryIds" item="categoryId" separator=" UNION ALL ">
                SELECT #{categoryId} AS product_category_id
            </foreach>
        ) cat
        LEFT JOIN product_group pg ON pg.product_category_id = cat.product_category_id
        LEFT JOIN material m ON m.group_id = pg.id
            AND m.create_time IS NOT NULL
            AND (
                CASE
                    WHEN #{queryDto.timeType} = 1 THEN
                        TO_CHAR(TO_TIMESTAMP(m.create_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYYMMDD') &lt;= tp.time_point
                    WHEN #{queryDto.timeType} = 2 THEN
                        TO_CHAR(TO_TIMESTAMP(m.create_time, 'YYYY-MM-DD HH24:MI:SS'), 'IYYY') ||
                        LPAD(TO_CHAR(TO_TIMESTAMP(m.create_time, 'YYYY-MM-DD HH24:MI:SS'), 'IW'), 2, '0') &lt;= tp.time_point
                    WHEN #{queryDto.timeType} = 3 THEN
                        TO_CHAR(TO_TIMESTAMP(m.create_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYYMM') &lt;= tp.time_point
                    WHEN #{queryDto.timeType} = 4 THEN
                        TO_CHAR(TO_TIMESTAMP(m.create_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY') &lt;= tp.time_point
                END
            )
        GROUP BY cat.product_category_id, tp.time_point
        ORDER BY cat.product_category_id, tp.time_point
    </select>
</mapper>